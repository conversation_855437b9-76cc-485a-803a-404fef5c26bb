# Prediction-Only Transformer Implementation Plan

## Executive Summary

This document provides a comprehensive implementation plan for transitioning the current transformer model from an imputation-based approach to a prediction-only approach. The goal is to eliminate gradient stability issues by removing missing value tokens from the computation graph while maintaining full compatibility with the existing ML pipeline.

**Key Objective**: Achieve stable transformer training with standard hyperparameters (LR 1e-3, gradient clipping 1.0) by processing only valid data points, while preserving all current functionality and interfaces.

## Project Overview

### **Current State Analysis**
- **Problem**: Transformer experiences non-finite gradient warnings due to missing value tokens (-999.0)
- **Current Approach**: Imputation-based transformer with aggressive stability measures (LR 1e-5, gradient clipping 0.5)
- **Dataset**: [489,630, 64, 5] tensor with ~26% missing values (40M+ missing points)
- **Pipeline**: Established ML pipeline with preprocessing, training, evaluation, and visualization components

### **Target State**
- **Solution**: Prediction-only transformer that processes only finite values
- **Expected Benefits**: Stable gradients, faster training, simplified architecture
- **Compatibility**: Zero breaking changes to existing interfaces and workflows
- **Performance**: Standard hyperparameters with improved training efficiency

## Implementation Strategy

### **Phase-Based Approach**
The implementation follows a 4-phase approach with clear deliverables, validation criteria, and rollback strategies for each phase.

### **Compatibility Principles**
1. **Interface Preservation**: All existing function signatures remain unchanged
2. **Workflow Continuity**: Current preprocessing and evaluation workflows preserved
3. **Output Compatibility**: Maintain expected output formats and structures
4. **Graceful Degradation**: Clear documentation of functional differences

## Phase 1: Core Architecture Development (Week 1)

### **Objectives**
- Develop prediction-only transformer architecture
- Implement adaptive positional encoding
- Create valid data extraction mechanisms
- Establish basic training loop modifications

### **Deliverables**

#### **1.1 Prediction-Only Transformer Core (Days 1-2)**
**File**: `models/advanced_models/prediction_transformer.py`

**Key Components**:
```python
class PredictionOnlyTransformer(nn.Module):
    """Transformer that processes only valid (finite) data points."""
    
    def __init__(self, input_dim=5, d_model=256, n_heads=8, n_layers=6):
        # Standard transformer architecture - no missing value handling
        
    def extract_valid_sequences(self, batch_data):
        """Extract only finite values and their original positions."""
        
    def create_prediction_samples(self, valid_sequences):
        """Create input-target pairs from valid sequences."""
        
    def forward(self, valid_inputs, original_positions):
        """Forward pass with only finite values."""
```

**Success Criteria**:
- [ ] Model initializes without errors
- [ ] Forward pass processes only finite values
- [ ] No missing value tokens in computation graph
- [ ] Basic gradient flow verification

#### **1.2 Adaptive Positional Encoding (Days 2-3)**
**File**: `models/advanced_models/adaptive_positional_encoding.py`

**Key Features**:
```python
class AdaptivePositionalEncoding(nn.Module):
    """Positional encoding for non-contiguous valid positions."""
    
    def forward(self, x, original_positions):
        """Apply encoding based on original sequence positions."""
```

**Success Criteria**:
- [ ] Handles non-contiguous position sequences
- [ ] Maintains temporal relationships
- [ ] Compatible with variable-length inputs
- [ ] Numerical stability verification

#### **1.3 Data Processing Pipeline (Days 3-4)**
**File**: `utils/prediction_data_processor.py`

**Key Functions**:
```python
def extract_valid_data_points(sequences, min_valid_points=5):
    """Extract valid points while preserving sequence relationships."""
    
def create_prediction_dataset(sequences, prediction_horizon=1):
    """Create prediction samples from valid sequences."""
    
def batch_valid_sequences(prediction_samples, batch_size=32):
    """Batch variable-length valid sequences efficiently."""
```

**Success Criteria**:
- [ ] Efficient valid data extraction
- [ ] Proper sequence relationship preservation
- [ ] Memory-efficient batching
- [ ] Compatible with existing data formats

### **Validation Criteria - Phase 1**
- [ ] All new components pass unit tests
- [ ] Memory usage within expected bounds
- [ ] No missing value tokens in any computation
- [ ] Basic training loop executes without errors
- [ ] Gradient norms remain finite throughout training

### **Rollback Strategy - Phase 1**
- Maintain original transformer model as fallback
- Use feature flags to switch between implementations
- Comprehensive logging for debugging
- Automated testing to catch regressions

## Phase 2: Pipeline Integration (Week 2)

### **Objectives**
- Integrate prediction-only transformer with existing ML pipeline
- Modify training loops while preserving interfaces
- Implement compatibility layers for seamless transition
- Establish evaluation framework adaptations

### **Deliverables**

#### **2.1 ML Core Integration (Days 1-2)**
**File**: `ml_core.py` (modifications)

**Key Modifications**:
```python
# Add prediction-only configuration
transformer_config = {
    'model_type': 'prediction_only',  # New parameter
    'learning_rate': 1e-3,            # Increased from 1e-5
    'gradient_clip_norm': 1.0,        # Increased from 0.5
    'use_prediction_only': True,      # Feature flag
    # ... existing parameters preserved
}

def get_model_config(model_name):
    """Enhanced to support prediction-only mode."""
    if model_name == 'transformer' and config.get('use_prediction_only', False):
        return prediction_only_transformer_config
    # ... existing logic preserved
```

**Success Criteria**:
- [ ] Existing model configurations unchanged
- [ ] New prediction-only configuration available
- [ ] Feature flag controls model selection
- [ ] All existing interfaces preserved

#### **2.2 Training Loop Adaptation (Days 2-3)**
**File**: `models/advanced_models/transformer_model.py` (modifications)

**Key Changes**:
```python
class TransformerModel:
    def __init__(self, **kwargs):
        # Detect prediction-only mode
        self.prediction_only_mode = kwargs.get('use_prediction_only', False)
        
        if self.prediction_only_mode:
            self.model = PredictionOnlyTransformer(**kwargs)
            self.data_processor = PredictionDataProcessor()
        else:
            # Existing imputation-based implementation
            
    def fit(self, X_train, y_train, **kwargs):
        """Enhanced fit method supporting both modes."""
        if self.prediction_only_mode:
            return self._fit_prediction_only(X_train, y_train, **kwargs)
        else:
            return self._fit_imputation_based(X_train, y_train, **kwargs)
```

**Success Criteria**:
- [ ] Backward compatibility maintained
- [ ] Prediction-only training stable
- [ ] Standard hyperparameters work
- [ ] No gradient warnings in prediction-only mode

#### **2.3 Evaluation Framework Adaptation (Days 3-4)**
**File**: `utils/evaluation_utils.py` (modifications)

**Key Enhancements**:
```python
def evaluate_model_performance(model, test_data, model_type='imputation'):
    """Enhanced evaluation supporting prediction-only models."""
    
    if hasattr(model, 'prediction_only_mode') and model.prediction_only_mode:
        return evaluate_prediction_only_model(model, test_data)
    else:
        return evaluate_imputation_model(model, test_data)

def evaluate_prediction_only_model(model, test_data):
    """Specialized evaluation for prediction-only models."""
    # Focus on prediction accuracy metrics
    # Handle missing value positions appropriately
```

**Success Criteria**:
- [ ] Evaluation metrics adapted for prediction-only mode
- [ ] Clear distinction between prediction and imputation metrics
- [ ] Existing evaluation workflows preserved
- [ ] Performance comparison framework established

### **Validation Criteria - Phase 2**
- [ ] All existing ML pipeline functions work unchanged
- [ ] Prediction-only mode trains without gradient warnings
- [ ] Performance metrics show expected improvements
- [ ] Memory usage within acceptable bounds
- [ ] Training speed improvements verified

### **Rollback Strategy - Phase 2**
- Feature flag allows instant rollback to original implementation
- Parallel testing with both implementations
- Performance monitoring and alerting
- Automated regression testing

## Phase 3: Visualization and Output Compatibility (Week 3)

### **Objectives**
- Adapt visualization functions for prediction-only outputs
- Implement clear labeling and documentation of mode differences
- Maintain all existing plot types and formats
- Ensure user experience continuity

### **Deliverables**

#### **3.1 Plotting Function Adaptations (Days 1-2)**
**Files**: `utils/plotting_utils.py`, `utils/visualization.py` (modifications)

**Key Enhancements**:
```python
def plot_imputation_results(model, data, save_path, **kwargs):
    """Enhanced plotting supporting prediction-only mode."""
    
    # Detect model type
    is_prediction_only = hasattr(model, 'prediction_only_mode') and model.prediction_only_mode
    
    if is_prediction_only:
        # Use prediction outputs for visualization
        # Add clear labeling: "Prediction-Only Mode"
        # Include explanatory annotations
        title_suffix = " (Prediction-Only Mode)"
        filename_suffix = "_prediction_only"
    else:
        # Existing imputation plotting logic
        title_suffix = ""
        filename_suffix = ""
    
    # Generate plots with appropriate labeling
    plt.title(f"Model Results{title_suffix}")
    plt.savefig(f"{save_path}{filename_suffix}.png")
```

**Success Criteria**:
- [ ] All existing plot types supported
- [ ] Clear mode identification in titles and filenames
- [ ] Explanatory annotations for prediction-only limitations
- [ ] Visual quality maintained

#### **3.2 Output Format Standardization (Days 2-3)**
**File**: `utils/output_formatter.py` (new)

**Key Functions**:
```python
def format_model_outputs(model, predictions, mode_info):
    """Standardize outputs across different model types."""
    
def add_prediction_only_metadata(outputs, model_info):
    """Add metadata explaining prediction-only limitations."""
    
def create_compatibility_layer(prediction_only_outputs, expected_format):
    """Convert prediction-only outputs to expected imputation format."""
```

**Success Criteria**:
- [ ] Consistent output formats across model types
- [ ] Clear metadata about model capabilities
- [ ] Seamless integration with existing workflows
- [ ] No breaking changes to downstream processes

### **Validation Criteria - Phase 3**
- [ ] All visualization functions work with both model types
- [ ] Clear differentiation between prediction and imputation outputs
- [ ] User documentation updated appropriately
- [ ] No confusion about model capabilities

### **Rollback Strategy - Phase 3**
- Visualization functions maintain backward compatibility
- Clear separation between prediction-only and imputation plotting
- Fallback to original plotting for imputation models
- User notification system for mode differences

## Phase 4: Performance Optimization and Production Readiness (Week 4)

### **Objectives**
- Optimize prediction-only transformer for production use
- Implement comprehensive testing and validation
- Establish monitoring and performance benchmarks
- Create documentation and user guides

### **Deliverables**

#### **4.1 Performance Optimization (Days 1-2)**
**Files**: Various optimization enhancements

**Key Optimizations**:
```python
# Memory optimization for variable-length sequences
def optimize_batch_processing(valid_sequences):
    """Efficient batching of variable-length valid sequences."""
    
# Computational optimization
def parallel_valid_data_extraction(batch_data):
    """Parallel processing of valid data extraction."""
    
# GPU memory optimization
def efficient_attention_computation(valid_inputs):
    """Optimized attention for non-contiguous sequences."""
```

**Success Criteria**:
- [ ] Training speed improvements verified (target: 5-10x faster)
- [ ] Memory usage optimized (target: 40-50% reduction)
- [ ] GPU utilization improved
- [ ] Scalability to larger datasets verified

#### **4.2 Comprehensive Testing Suite (Days 2-3)**
**File**: `tests/test_prediction_only_transformer.py` (new)

**Test Categories**:
```python
class TestPredictionOnlyTransformer:
    def test_gradient_stability(self):
        """Verify no gradient warnings in prediction-only mode."""
        
    def test_hyperparameter_compatibility(self):
        """Verify standard hyperparameters work."""
        
    def test_pipeline_compatibility(self):
        """Verify existing pipeline integration."""
        
    def test_performance_benchmarks(self):
        """Verify performance improvements."""
```

**Success Criteria**:
- [ ] 100% test coverage for new components
- [ ] All gradient stability tests pass
- [ ] Performance benchmarks met
- [ ] Compatibility tests pass

#### **4.3 Documentation and User Guide (Days 3-4)**
**Files**: Documentation updates

**Documentation Components**:
- User guide for prediction-only mode
- Performance comparison documentation
- Migration guide from imputation-based approach
- Troubleshooting and FAQ

**Success Criteria**:
- [ ] Complete user documentation
- [ ] Clear migration instructions
- [ ] Performance benchmarks documented
- [ ] Limitations clearly explained

### **Validation Criteria - Phase 4**
- [ ] Production-ready performance achieved
- [ ] Comprehensive testing suite passes
- [ ] Documentation complete and accurate
- [ ] User acceptance testing successful

### **Rollback Strategy - Phase 4**
- Complete rollback procedures documented
- Performance monitoring and alerting
- User feedback collection and response
- Continuous improvement process established

## Success Metrics and Benchmarks

### **Primary Success Criteria**
1. **Gradient Stability**: Zero non-finite gradient warnings in prediction-only mode
2. **Training Performance**: Learning rate 1e-3 with stable convergence
3. **Computational Efficiency**: 40-50% reduction in memory usage, 5-10x training speed improvement
4. **Pipeline Compatibility**: 100% backward compatibility with existing interfaces

### **Performance Benchmarks**
- **Training Speed**: Target 5-10x improvement over current imputation-based approach
- **Memory Usage**: Target 40-50% reduction in GPU memory consumption
- **Gradient Stability**: Zero gradient warnings across all test datasets
- **Prediction Accuracy**: Maintain or improve prediction accuracy on valid data points

### **Quality Assurance**
- Comprehensive unit and integration testing
- Performance regression testing
- User acceptance testing
- Documentation review and validation

## Risk Mitigation and Contingency Plans

### **Technical Risks**
1. **Performance Degradation**: Continuous monitoring and optimization
2. **Compatibility Issues**: Extensive testing and gradual rollout
3. **User Confusion**: Clear documentation and training
4. **Data Quality Issues**: Robust validation and error handling

### **Mitigation Strategies**
- Feature flags for instant rollback
- Parallel testing with existing implementation
- Comprehensive monitoring and alerting
- User feedback collection and response

## Timeline and Resource Allocation

### **4-Week Implementation Schedule**
- **Week 1**: Core architecture development and basic integration
- **Week 2**: Pipeline integration and evaluation framework
- **Week 3**: Visualization compatibility and output formatting
- **Week 4**: Performance optimization and production readiness

### **Resource Requirements**
- 1 Senior ML Engineer (full-time)
- 1 Software Engineer for testing and integration (part-time)
- 1 Technical Writer for documentation (part-time)
- Access to development and testing environments

## Detailed Code Implementation Specifications

### **Core Architecture Implementation Details**

#### **PredictionOnlyTransformer Class Structure**
```python
# File: models/advanced_models/prediction_transformer.py

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Dict, List, Tuple, Optional

class PredictionOnlyTransformer(nn.Module):
    """
    Transformer architecture optimized for prediction-only tasks.
    Eliminates missing value tokens from computation graph for gradient stability.
    """

    def __init__(self,
                 input_dim: int = 5,
                 d_model: int = 256,
                 n_heads: int = 8,
                 n_layers: int = 6,
                 d_ff: int = 1024,
                 dropout: float = 0.1,
                 max_seq_len: int = 64):
        super().__init__()

        self.input_dim = input_dim
        self.d_model = d_model
        self.max_seq_len = max_seq_len

        # Input projection layer
        self.input_projection = nn.Linear(input_dim, d_model)

        # Adaptive positional encoding
        self.pos_encoding = AdaptivePositionalEncoding(d_model, max_seq_len)

        # Standard transformer encoder layers
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_ff,
            dropout=dropout,
            batch_first=True,
            activation='gelu'  # More stable than ReLU
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, n_layers)

        # Prediction head
        self.prediction_head = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_ff),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, input_dim)
        )

        # Initialize weights
        self._init_weights()

    def _init_weights(self):
        """Initialize model weights for stable training."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)

    def extract_valid_sequences(self, batch_sequences: torch.Tensor) -> List[Dict]:
        """
        Extract valid (finite) data points from batch sequences.

        Args:
            batch_sequences: [batch_size, seq_len, input_dim] with potential NaN values

        Returns:
            List of dictionaries containing valid sequences and metadata
        """
        batch_results = []

        for seq_idx, sequence in enumerate(batch_sequences):
            # Find positions with all finite values across all features
            valid_mask = torch.isfinite(sequence).all(dim=-1)  # [seq_len]
            valid_positions = torch.where(valid_mask)[0]

            if len(valid_positions) >= 2:  # Need at least 2 points for prediction
                valid_values = sequence[valid_positions]  # [n_valid, input_dim]

                batch_results.append({
                    'values': valid_values,
                    'positions': valid_positions,
                    'sequence_idx': seq_idx,
                    'original_length': len(sequence),
                    'valid_count': len(valid_positions)
                })

        return batch_results

    def create_prediction_samples(self, valid_sequences: List[Dict],
                                min_context: int = 3) -> List[Dict]:
        """
        Create input-target pairs from valid sequences for prediction training.

        Args:
            valid_sequences: List of valid sequence dictionaries
            min_context: Minimum context length for prediction

        Returns:
            List of prediction sample dictionaries
        """
        prediction_samples = []

        for seq_data in valid_sequences:
            values = seq_data['values']
            positions = seq_data['positions']

            # Create multiple prediction samples from each sequence
            for i in range(min_context, len(values)):
                context_values = values[:i]      # [context_len, input_dim]
                target_value = values[i]         # [input_dim]
                context_positions = positions[:i]
                target_position = positions[i]

                prediction_samples.append({
                    'context': context_values,
                    'target': target_value,
                    'context_positions': context_positions,
                    'target_position': target_position,
                    'sequence_idx': seq_data['sequence_idx']
                })

        return prediction_samples

    def forward(self, context_sequences: torch.Tensor,
                context_positions: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Forward pass with only valid (finite) values.

        Args:
            context_sequences: [batch_size, max_context_len, input_dim] - only finite values
            context_positions: [batch_size, max_context_len] - original positions
            attention_mask: [batch_size, max_context_len] - padding mask

        Returns:
            predictions: [batch_size, input_dim] - predicted next values
        """
        # Input projection
        x = self.input_projection(context_sequences)  # [batch_size, seq_len, d_model]

        # Add adaptive positional encoding
        x = self.pos_encoding(x, context_positions)

        # Apply transformer encoder
        if attention_mask is not None:
            # Convert padding mask to attention mask format
            attention_mask = attention_mask.unsqueeze(1).unsqueeze(2)
            attention_mask = attention_mask.expand(-1, x.size(1), -1, -1)

        transformer_output = self.transformer_encoder(x, src_key_padding_mask=~attention_mask.squeeze() if attention_mask is not None else None)

        # Use last valid position for prediction
        if attention_mask is not None:
            # Find last valid position for each sequence
            last_valid_indices = attention_mask.sum(dim=-1) - 1  # [batch_size]
            last_hidden = transformer_output[torch.arange(transformer_output.size(0)), last_valid_indices]
        else:
            last_hidden = transformer_output[:, -1, :]  # [batch_size, d_model]

        # Generate predictions
        predictions = self.prediction_head(last_hidden)  # [batch_size, input_dim]

        return predictions

class AdaptivePositionalEncoding(nn.Module):
    """
    Positional encoding that adapts to non-contiguous valid positions.
    Maintains temporal relationships despite missing values.
    """

    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        self.d_model = d_model

        # Pre-compute positional encodings for all possible positions
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)

        div_term = torch.exp(torch.arange(0, d_model, 2).float() *
                           (-math.log(10000.0) / d_model))

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)

        self.register_buffer('pe', pe)

    def forward(self, x: torch.Tensor, original_positions: torch.Tensor) -> torch.Tensor:
        """
        Apply positional encoding based on original sequence positions.

        Args:
            x: [batch_size, seq_len, d_model] - input embeddings
            original_positions: [batch_size, seq_len] - original position indices

        Returns:
            x + positional_encoding: [batch_size, seq_len, d_model]
        """
        batch_size, seq_len, d_model = x.shape
        pos_encoding = torch.zeros_like(x)

        for i in range(batch_size):
            for j in range(seq_len):
                if j < original_positions.size(1):
                    pos_idx = original_positions[i, j].long()
                    if pos_idx < self.pe.size(0):
                        pos_encoding[i, j] = self.pe[pos_idx]

        return x + pos_encoding
```

#### **Data Processing Pipeline Implementation**
```python
# File: utils/prediction_data_processor.py

import torch
import numpy as np
from typing import List, Dict, Tuple, Optional
from torch.utils.data import Dataset, DataLoader

class PredictionDataProcessor:
    """
    Data processor for prediction-only transformer training.
    Handles extraction and batching of valid data points.
    """

    def __init__(self, min_valid_points: int = 5, min_context: int = 3):
        self.min_valid_points = min_valid_points
        self.min_context = min_context

    def process_sequences(self, sequences: np.ndarray) -> List[Dict]:
        """
        Process raw sequences to extract valid prediction samples.

        Args:
            sequences: [n_sequences, seq_len, n_features] with potential NaN values

        Returns:
            List of prediction sample dictionaries
        """
        prediction_samples = []

        for seq_idx, sequence in enumerate(sequences):
            # Convert to tensor for processing
            seq_tensor = torch.from_numpy(sequence).float()

            # Find valid positions (all features finite)
            valid_mask = torch.isfinite(seq_tensor).all(dim=-1)
            valid_positions = torch.where(valid_mask)[0]

            if len(valid_positions) >= self.min_valid_points:
                valid_values = seq_tensor[valid_positions]

                # Create prediction samples
                for i in range(self.min_context, len(valid_values)):
                    context_values = valid_values[:i]
                    target_value = valid_values[i]
                    context_positions = valid_positions[:i]
                    target_position = valid_positions[i]

                    prediction_samples.append({
                        'context': context_values,
                        'target': target_value,
                        'context_positions': context_positions,
                        'target_position': target_position,
                        'sequence_idx': seq_idx,
                        'context_length': len(context_values)
                    })

        return prediction_samples

    def create_batches(self, prediction_samples: List[Dict],
                      batch_size: int = 32) -> List[Dict]:
        """
        Create batches from prediction samples with padding for variable lengths.

        Args:
            prediction_samples: List of prediction sample dictionaries
            batch_size: Batch size for training

        Returns:
            List of batch dictionaries
        """
        # Sort by context length for efficient batching
        prediction_samples.sort(key=lambda x: x['context_length'])

        batches = []
        for i in range(0, len(prediction_samples), batch_size):
            batch_samples = prediction_samples[i:i + batch_size]

            # Find maximum context length in batch
            max_context_len = max(sample['context_length'] for sample in batch_samples)

            # Initialize batch tensors
            batch_contexts = torch.zeros(len(batch_samples), max_context_len,
                                       batch_samples[0]['context'].shape[-1])
            batch_targets = torch.stack([sample['target'] for sample in batch_samples])
            batch_positions = torch.zeros(len(batch_samples), max_context_len, dtype=torch.long)
            batch_masks = torch.zeros(len(batch_samples), max_context_len, dtype=torch.bool)

            # Fill batch tensors
            for j, sample in enumerate(batch_samples):
                context_len = sample['context_length']
                batch_contexts[j, :context_len] = sample['context']
                batch_positions[j, :context_len] = sample['context_positions']
                batch_masks[j, :context_len] = True

            batches.append({
                'contexts': batch_contexts,
                'targets': batch_targets,
                'positions': batch_positions,
                'masks': batch_masks,
                'sequence_indices': [sample['sequence_idx'] for sample in batch_samples]
            })

        return batches

class PredictionDataset(Dataset):
    """PyTorch Dataset for prediction-only training."""

    def __init__(self, prediction_samples: List[Dict]):
        self.samples = prediction_samples

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        return self.samples[idx]

def collate_prediction_samples(batch: List[Dict]) -> Dict[str, torch.Tensor]:
    """
    Custom collate function for variable-length prediction samples.

    Args:
        batch: List of prediction sample dictionaries

    Returns:
        Batched tensors with appropriate padding
    """
    # Find maximum context length
    max_context_len = max(sample['context_length'] for sample in batch)

    # Initialize batch tensors
    contexts = torch.zeros(len(batch), max_context_len, batch[0]['context'].shape[-1])
    targets = torch.stack([sample['target'] for sample in batch])
    positions = torch.zeros(len(batch), max_context_len, dtype=torch.long)
    masks = torch.zeros(len(batch), max_context_len, dtype=torch.bool)

    # Fill tensors
    for i, sample in enumerate(batch):
        context_len = sample['context_length']
        contexts[i, :context_len] = sample['context']
        positions[i, :context_len] = sample['context_positions']
        masks[i, :context_len] = True

    return {
        'contexts': contexts,
        'targets': targets,
        'positions': positions,
        'masks': masks,
        'sequence_indices': [sample['sequence_idx'] for sample in batch]
    }
```

#### **Training Loop Integration**
```python
# File: models/advanced_models/transformer_model.py (modifications)

class TransformerModel:
    def __init__(self, **kwargs):
        # ... existing initialization ...

        # Detect prediction-only mode
        self.prediction_only_mode = kwargs.get('use_prediction_only', False)

        if self.prediction_only_mode:
            # Initialize prediction-only components
            self.model = PredictionOnlyTransformer(
                input_dim=kwargs.get('input_dim', 5),
                d_model=kwargs.get('d_model', 256),
                n_heads=kwargs.get('n_heads', 8),
                n_layers=kwargs.get('n_layers', 6),
                dropout=kwargs.get('dropout', 0.1)
            )
            self.data_processor = PredictionDataProcessor()

            # Use standard hyperparameters for stable training
            self.learning_rate = kwargs.get('learning_rate', 1e-3)  # Higher LR
            self.gradient_clip_norm = kwargs.get('gradient_clip_norm', 1.0)  # Standard clipping
        else:
            # Existing imputation-based initialization
            # ... existing code ...

    def _fit_prediction_only(self, X_train, y_train, **kwargs):
        """
        Training method for prediction-only transformer.

        Args:
            X_train: Training sequences with potential missing values
            y_train: Target sequences (not used in prediction-only mode)
            **kwargs: Additional training parameters

        Returns:
            Training history and metrics
        """
        # Process training data
        prediction_samples = self.data_processor.process_sequences(X_train)

        if not prediction_samples:
            raise ValueError("No valid prediction samples found in training data")

        # Create dataset and dataloader
        dataset = PredictionDataset(prediction_samples)
        dataloader = DataLoader(
            dataset,
            batch_size=kwargs.get('batch_size', 32),
            shuffle=True,
            collate_fn=collate_prediction_samples
        )

        # Initialize optimizer with standard parameters
        optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=self.learning_rate,
            weight_decay=kwargs.get('weight_decay', 1e-4)
        )

        # Learning rate scheduler
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=kwargs.get('epochs', 100)
        )

        # Loss function
        criterion = nn.MSELoss()

        # Training loop
        training_history = []

        for epoch in range(kwargs.get('epochs', 100)):
            epoch_losses = []

            for batch in dataloader:
                # Move to device
                contexts = batch['contexts'].to(self.device)
                targets = batch['targets'].to(self.device)
                positions = batch['positions'].to(self.device)
                masks = batch['masks'].to(self.device)

                # Forward pass - no missing values in computation graph
                predictions = self.model(contexts, positions, masks)

                # Compute loss
                loss = criterion(predictions, targets)

                # Backward pass with standard gradient handling
                optimizer.zero_grad()
                loss.backward()

                # Standard gradient clipping (should be stable now)
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.gradient_clip_norm)

                optimizer.step()

                epoch_losses.append(loss.item())

            # Update learning rate
            scheduler.step()

            # Record training metrics
            avg_loss = np.mean(epoch_losses)
            training_history.append({
                'epoch': epoch,
                'loss': avg_loss,
                'learning_rate': scheduler.get_last_lr()[0]
            })

            # Log progress
            if epoch % 10 == 0:
                print(f"Epoch {epoch}: Loss = {avg_loss:.6f}, LR = {scheduler.get_last_lr()[0]:.6f}")

        return training_history

    def predict(self, X_test):
        """
        Prediction method supporting both modes.

        Args:
            X_test: Test sequences

        Returns:
            Predictions in format compatible with existing pipeline
        """
        if self.prediction_only_mode:
            return self._predict_prediction_only(X_test)
        else:
            return self._predict_imputation_based(X_test)

    def _predict_prediction_only(self, X_test):
        """
        Prediction method for prediction-only transformer.

        Args:
            X_test: Test sequences with potential missing values

        Returns:
            Predictions for valid positions, formatted for compatibility
        """
        self.model.eval()
        predictions = []

        with torch.no_grad():
            # Process test sequences
            test_samples = self.data_processor.process_sequences(X_test)

            if not test_samples:
                # Return empty predictions if no valid samples
                return np.full_like(X_test, np.nan)

            # Create batches
            test_batches = self.data_processor.create_batches(test_samples, batch_size=64)

            batch_predictions = []
            for batch in test_batches:
                contexts = batch['contexts'].to(self.device)
                positions = batch['positions'].to(self.device)
                masks = batch['masks'].to(self.device)

                # Generate predictions
                batch_pred = self.model(contexts, positions, masks)
                batch_predictions.append(batch_pred.cpu().numpy())

            # Combine all predictions
            all_predictions = np.concatenate(batch_predictions, axis=0)

            # Format predictions to match expected output shape
            formatted_predictions = self._format_predictions_for_compatibility(
                all_predictions, test_samples, X_test.shape
            )

        return formatted_predictions

    def _format_predictions_for_compatibility(self, predictions, test_samples, original_shape):
        """
        Format prediction-only outputs to match expected pipeline format.

        Args:
            predictions: Raw model predictions
            test_samples: Test sample metadata
            original_shape: Original input shape

        Returns:
            Formatted predictions compatible with existing pipeline
        """
        # Initialize output with NaN (indicating no prediction available)
        formatted_output = np.full(original_shape, np.nan)

        # Fill in predictions at appropriate positions
        for i, (pred, sample) in enumerate(zip(predictions, test_samples)):
            seq_idx = sample['sequence_idx']
            target_pos = sample['target_position']

            # Place prediction at target position
            formatted_output[seq_idx, target_pos] = pred

        return formatted_output
```

## Conclusion

This implementation plan provides a comprehensive roadmap for transitioning to a prediction-only transformer approach while maintaining full compatibility with the existing ML pipeline. The phased approach ensures minimal risk and maximum compatibility, with clear success criteria and rollback strategies for each phase.

The expected outcome is a stable, efficient transformer model that eliminates gradient stability issues while preserving all current functionality and improving overall performance.
