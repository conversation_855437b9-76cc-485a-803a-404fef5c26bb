"""
Phase 1 Integration for ML Core

This module provides Phase 1 enhanced versions of the core ML functions
that integrate advanced data preprocessing to eliminate non-finite gradient issues.

Usage:
    Replace calls to impute_logs_deep with impute_logs_deep_phase1
"""

import numpy as np
import pandas as pd
import torch
from typing import Dict, Any, Optional, List, Tuple

# Import original functions
from ml_core import impute_logs_deep as original_impute_logs_deep
from data_handler import create_sequences, normalize_data, introduce_missingness

# Import Phase 1 preprocessing
try:
    from utils.stability_core import (
        phase1_preprocessing_pipeline,
        enhanced_validate_sequences,
        get_recommended_preprocessing_config,
        numerical_stability_check
    )
    PHASE1_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Phase 1 preprocessing not available: {e}")
    PHASE1_AVAILABLE = False


def impute_logs_deep_phase1(df: pd.DataFrame, 
                           feature_cols: List[str], 
                           target_col: str, 
                           model_config: Dict[str, Any], 
                           hparams: Dict[str, Any], 
                           use_enhanced_preprocessing: bool = True) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Enhanced version of impute_logs_deep with Phase 1 preprocessing integration.
    
    This function addresses the non-finite gradient issues by:
    1. Applying Phase 1 preprocessing to eliminate problematic data
    2. Validating sequences before training
    3. Monitoring for stability issues during processing
    
    Args:
        df: Input dataframe
        feature_cols: Feature column names
        target_col: Target column name
        model_config: Model configuration
        hparams: Hyperparameters
        use_enhanced_preprocessing: Whether to use enhanced preprocessing
        
    Returns:
        Tuple of (result_dataframe, model_results)
    """
    if not PHASE1_AVAILABLE:
        print("⚠️ Phase 1 preprocessing not available, falling back to original function")
        return original_impute_logs_deep(df, feature_cols, target_col, model_config, hparams, use_enhanced_preprocessing)
    
    print("🚀 Starting Phase 1 Enhanced Deep Learning Training...")
    print(f"   Model: {model_config['name']}")
    print(f"   Target: {target_col}")
    print(f"   Features: {feature_cols}")
    
    # Step 1: Original preprocessing (up to sequence creation)
    print("\n📊 Step 1: Initial Data Preparation...")
    
    # Get all features including target
    all_features = feature_cols + [target_col]
    
    # Normalize data using existing function
    df_scaled, scalers = normalize_data(df, all_features, use_enhanced=use_enhanced_preprocessing)
    
    # Create sequences using existing function
    train_sequences_true, metadata = create_sequences(
        df_scaled, 'WELL', all_features, 
        sequence_len=hparams.get('sequence_len', 64),
        use_enhanced=use_enhanced_preprocessing
    )
    
    if train_sequences_true.shape[0] == 0:
        print("❌ ERROR: Cannot create training sequences. Falling back to original function.")
        return original_impute_logs_deep(df, feature_cols, target_col, model_config, hparams, use_enhanced_preprocessing)
    
    print(f"   Created {train_sequences_true.shape[0]} sequences")
    print(f"   Sequence shape: {train_sequences_true.shape}")
    
    # Step 2: Apply Phase 1 Preprocessing Pipeline
    print("\n🔍 Step 2: Phase 1 Advanced Preprocessing...")
    
    # Get recommended configuration for this dataset
    missing_rate = np.sum(np.isnan(train_sequences_true)) / np.prod(train_sequences_true.shape)
    config = get_recommended_preprocessing_config(
        dataset_size=train_sequences_true.shape[0],
        missing_rate=missing_rate,
        feature_types=all_features
    )
    
    print(f"   Dataset characteristics:")
    print(f"     • Sequences: {train_sequences_true.shape[0]}")
    print(f"     • Missing rate: {missing_rate:.1%}")
    print(f"     • Recommended config: {config}")
    
    # Apply Phase 1 preprocessing pipeline
    processed_sequences, processing_metadata = phase1_preprocessing_pipeline(
        sequences=train_sequences_true,
        feature_names=all_features,
        normalization_method=config.get('normalization_method', 'robust_standard'),
        missing_encoding_method=config.get('missing_encoding_method', 'learnable_embedding'),
        validate_ranges=config.get('validate_ranges', True),
        generate_report=False  # Skip detailed report for speed
    )
    
    print(f"✅ Phase 1 preprocessing completed:")
    print(f"   • Data quality score: {processing_metadata['reports']['validation']['data_quality_score']:.3f}")
    print(f"   • Missing rate: {processing_metadata['reports']['encoding']['missing_rate_before']:.1%} → {processing_metadata['reports']['encoding']['missing_rate_after']:.1%}")
    print(f"   • Final stability: {'✅ STABLE' if processing_metadata['final_stability']['is_stable'] else '❌ UNSTABLE'}")
    
    # Step 3: Create missing sequences for training
    print("\n❓ Step 3: Creating Training Sequences with Missing Values...")
    
    # Use the processed sequences as the "true" sequences
    train_sequences_missing = introduce_missingness(
        processed_sequences, 
        target_col_name=target_col,
        feature_names=all_features, 
        missing_rate=0.3, 
        use_enhanced=use_enhanced_preprocessing
    )
    
    print(f"   Training sequences with missing values: {train_sequences_missing.shape}")

    # Step 3.5: Encode artificial missing values for training
    # This prevents non-finite gradients by replacing raw NaNs with model-friendly tokens
    print("\n🔧 Step 3.5: Encoding artificial missing values for training...")
    from utils.stability_core import encode_missing_values
    
    # Use 'masking_tokens' method to replace NaN with a specific value (-999.0)
    train_sequences_for_model, encoding_meta_final = encode_missing_values(
        train_sequences_missing,
        method='masking_tokens', # This replaces NaN with -999.0
        feature_names=all_features
    )
    print(f"   ✅ Final training sequences encoded. Missing values are now represented as tokens.")
    print(f"   Missing values before: {encoding_meta_final['missing_count_before']} -> After: {encoding_meta_final['missing_count_after']}")
    
    # Step 4: Enhanced Pre-training Validation
    print("\n🔧 Step 4: Enhanced Pre-training Validation...")

    # Validate training sequences (allow intentional missing values)
    train_stable = validate_training_sequences(
        train_sequences_missing,
        all_features,
        allow_missing=True,  # Training sequences can have missing values
        max_missing_rate=0.5  # Allow up to 50% missing for training
    )

    # Validate truth sequences (should have no missing values after Phase 1)
    truth_stable = validate_training_sequences(
        processed_sequences,
        all_features,
        allow_missing=False,  # Truth sequences should be complete
        max_missing_rate=0.0
    )

    print(f"   Training sequences: {'✅ STABLE' if train_stable else '❌ UNSTABLE'}")
    print(f"   Truth sequences: {'✅ STABLE' if truth_stable else '❌ UNSTABLE'}")

    if not train_stable:
        print("⚠️ WARNING: Training sequences failed stability check!")
        print("   This indicates issues with the artificially introduced missing values.")

        # Provide detailed diagnostics for training sequences
        missing_rate = np.sum(np.isnan(train_sequences_missing)) / np.prod(train_sequences_missing.shape)
        finite_count = np.sum(np.isfinite(train_sequences_missing))
        print(f"   Training diagnostics:")
        print(f"     • Missing rate: {missing_rate:.1%}")
        print(f"     • Finite values: {finite_count:,}")
        print(f"     • Shape: {train_sequences_missing.shape}")

    if not truth_stable:
        print("⚠️ WARNING: Truth sequences failed stability check!")
        print("   This indicates issues with Phase 1 preprocessing.")

        # Provide detailed diagnostics for truth sequences
        truth_missing_rate = np.sum(np.isnan(processed_sequences)) / np.prod(processed_sequences.shape)
        truth_finite_count = np.sum(np.isfinite(processed_sequences))
        print(f"   Truth diagnostics:")
        print(f"     • Missing rate: {truth_missing_rate:.1%}")
        print(f"     • Finite values: {truth_finite_count:,}")
        print(f"     • Shape: {processed_sequences.shape}")

    # Continue training even if training sequences have missing values (that's expected)
    # Only stop if truth sequences are unstable (that indicates a real problem)
    if not truth_stable:
        print("❌ CRITICAL: Truth sequences are unstable - cannot proceed with training")
        print("   Falling back to original function...")
        return original_impute_logs_deep(df, feature_cols, target_col, model_config, hparams, use_enhanced_preprocessing)

    # Step 5: Convert to tensors and call original training function
    print("\n🚀 Step 5: Enhanced Model Training...")
    
    # Convert to tensors
    train_tensor = torch.tensor(train_sequences_missing, dtype=torch.float32)
    truth_tensor = torch.tensor(processed_sequences, dtype=torch.float32)
    
    print(f"   Training tensor shape: {train_tensor.shape}")
    print(f"   Truth tensor shape: {truth_tensor.shape}")
    print(f"   Missing values in training: {torch.isnan(train_tensor).sum().item()}")
    print(f"   Missing values in truth: {torch.isnan(truth_tensor).sum().item()}")
    
    # Create a modified model config that uses the processed data
    enhanced_model_config = model_config.copy()
    enhanced_hparams = hparams.copy()
    
    # Add Phase 1 metadata to model results
    phase1_metadata = {
        'phase1_applied': True,
        'preprocessing_config': config,
        'data_quality_score': processing_metadata['reports']['validation']['data_quality_score'],
        'missing_rate_reduction': f"{processing_metadata['reports']['encoding']['missing_rate_before']:.1%} → {processing_metadata['reports']['encoding']['missing_rate_after']:.1%}",
        'stability_check': processing_metadata['final_stability']['is_stable']
    }
    
    # Temporarily replace the sequences in a way that the original function can use them
    # We'll need to modify the approach since the original function expects DataFrame input
    
    # For now, let's create a synthetic DataFrame that represents our processed sequences
    # This is a workaround to integrate with the existing function structure
    
    print("   Preparing enhanced data for model training...")
    
    # Create a temporary DataFrame from processed sequences for compatibility
    n_sequences, seq_len, n_features = processed_sequences.shape
    
    # Flatten sequences back to DataFrame format
    flattened_data = []
    for seq_idx in range(n_sequences):
        well_name = f"WELL_{seq_idx // 10}"  # Group sequences by synthetic wells
        for time_idx in range(seq_len):
            row_data = {'WELL': well_name, 'MD': time_idx}
            for feat_idx, feat_name in enumerate(all_features):
                row_data[feat_name] = processed_sequences[seq_idx, time_idx, feat_idx]
            flattened_data.append(row_data)
    
    processed_df = pd.DataFrame(flattened_data)
    
    print(f"   Created processed DataFrame: {processed_df.shape}")
    print(f"   Wells: {processed_df['WELL'].nunique()}")
    
    # Call original function with processed data
    print("\n🎯 Calling enhanced model training...")
    try:
        result_df, model_results = original_impute_logs_deep(
            processed_df, feature_cols, target_col, enhanced_model_config, enhanced_hparams, use_enhanced_preprocessing
        )
        
        # Add Phase 1 metadata to results
        if model_results:
            model_results['phase1_metadata'] = phase1_metadata
            model_results['original_missing_count'] = int(np.sum(np.isnan(train_sequences_true)))
            model_results['processed_missing_count'] = int(np.sum(np.isnan(processed_sequences)))
            
            print("\n✅ Phase 1 Enhanced Training Completed Successfully!")
            print(f"   Original missing values: {model_results['original_missing_count']:,}")
            print(f"   Processed missing values: {model_results['processed_missing_count']:,}")
            print(f"   Data quality improvement: {phase1_metadata['data_quality_score']:.3f}")
            
        return result_df, model_results
        
    except Exception as e:
        print(f"❌ Enhanced training failed: {e}")
        print("   Falling back to original function...")
        return original_impute_logs_deep(df, feature_cols, target_col, model_config, hparams, use_enhanced_preprocessing)


def validate_training_sequences(sequences: np.ndarray,
                              feature_names: List[str],
                              allow_missing: bool = True,
                              max_missing_rate: float = 0.5) -> bool:
    """
    Training-aware validation that allows intentional missing values.

    Args:
        sequences: Input sequences to validate
        feature_names: List of feature names
        allow_missing: Whether to allow missing values (True for training sequences)
        max_missing_rate: Maximum allowed missing rate for training sequences

    Returns:
        True if sequences are safe for training, False otherwise
    """
    if not PHASE1_AVAILABLE:
        return True  # Skip validation if Phase 1 not available

    # For training sequences with intentional missing values, use custom validation
    if allow_missing:
        return validate_training_sequences_with_missing(sequences, feature_names, max_missing_rate)
    else:
        # For truth sequences, use standard validation (no missing values allowed)
        return enhanced_validate_sequences(sequences, feature_names)


def validate_training_sequences_with_missing(sequences: np.ndarray,
                                           feature_names: List[str],
                                           max_missing_rate: float = 0.5) -> bool:
    """
    Validate training sequences that intentionally contain missing values.

    This function checks for stability issues while allowing controlled missing values.

    Args:
        sequences: Training sequences with intentional missing values
        feature_names: List of feature names
        max_missing_rate: Maximum allowed missing rate

    Returns:
        True if sequences are safe for training despite missing values
    """
    try:
        # Basic shape validation
        if len(sequences.shape) != 3:
            print(f"   ❌ Invalid shape: {sequences.shape} (expected 3D)")
            return False

        # Check missing rate
        total_values = np.prod(sequences.shape)
        missing_count = np.sum(np.isnan(sequences))
        missing_rate = missing_count / total_values

        if missing_rate > max_missing_rate:
            print(f"   ❌ Missing rate too high: {missing_rate:.1%} > {max_missing_rate:.1%}")
            return False

        # Check non-missing values for stability issues
        finite_data = sequences[np.isfinite(sequences)]

        if len(finite_data) == 0:
            print(f"   ❌ No finite values found")
            return False

        # Check for extreme values in non-missing data
        max_abs = np.max(np.abs(finite_data))
        if max_abs > 1e6:
            print(f"   ❌ Extreme values detected: max_abs = {max_abs:.2e}")
            return False

        # Check for infinite values (not allowed even in training)
        inf_count = np.sum(np.isinf(sequences))
        if inf_count > 0:
            print(f"   ❌ Contains {inf_count} infinite values")
            return False

        # Check value distribution for each feature
        n_sequences, seq_len, n_features = sequences.shape
        for feat_idx, feat_name in enumerate(feature_names[:n_features]):
            feature_data = sequences[:, :, feat_idx]
            finite_feature_data = feature_data[np.isfinite(feature_data)]

            if len(finite_feature_data) > 0:
                std_val = np.std(finite_feature_data)
                if std_val > 100:  # Reasonable threshold for normalized data
                    print(f"   ⚠️ High variance in {feat_name}: std = {std_val:.2f}")
                    # Don't fail, just warn

        print(f"   ✅ Training sequences validated: {missing_rate:.1%} missing rate, {len(finite_data):,} finite values")
        return True

    except Exception as e:
        print(f"   ❌ Validation error: {e}")
        return False


def validate_batch_before_training(batch_data: torch.Tensor,
                                 batch_idx: int,
                                 feature_names: List[str]) -> bool:
    """
    Validate a batch before training to prevent non-finite gradient issues.

    Args:
        batch_data: Batch tensor to validate
        batch_idx: Batch index for logging
        feature_names: List of feature names

    Returns:
        True if batch is safe for training, False otherwise
    """
    if not PHASE1_AVAILABLE:
        return True  # Skip validation if Phase 1 not available

    # Convert to numpy for validation
    if isinstance(batch_data, torch.Tensor):
        batch_np = batch_data.detach().cpu().numpy()
    else:
        batch_np = np.array(batch_data)

    # Use training-aware validation that allows missing values
    is_stable = validate_training_sequences_with_missing(batch_np, feature_names, max_missing_rate=0.8)

    if not is_stable:
        print(f"⚠️ Batch {batch_idx} failed stability check - skipping to prevent gradient issues")

    return is_stable


# Export enhanced functions
__all__ = [
    'impute_logs_deep_phase1',
    'validate_training_sequences',
    'validate_training_sequences_with_missing',
    'validate_batch_before_training'
]
