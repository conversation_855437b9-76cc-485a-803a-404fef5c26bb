#!/usr/bin/env python3
"""
Test script to verify the stability fixes for non-finite gradient issues.
This script tests the enhanced transformer model with Phase 1 preprocessing.
"""

import numpy as np
import torch
import sys
import os

def test_stability_components():
    """Test that the stability components are properly imported and initialized."""
    print("🧪 Testing Stability Components...")
    
    try:
        from utils.stability_core import (
            UniversalGradientClipper, 
            AdaptiveLRScheduler, 
            enhanced_training_step,
            validate_batch_before_training,
            phase1_preprocessing_pipeline
        )
        print("✅ All stability components imported successfully")
        
        # Test gradient clipper
        clipper = UniversalGradientClipper('transformer', max_norm=0.5)
        print(f"✅ Gradient clipper initialized: max_norm={clipper.max_norm}")
        
        # Test LR scheduler (dummy optimizer)
        dummy_optimizer = torch.optim.Adam([torch.randn(10, requires_grad=True)], lr=1e-5)
        scheduler = AdaptiveLRScheduler(dummy_optimizer, 'transformer', warmup_steps=100)
        print(f"✅ LR scheduler initialized: warmup_steps={scheduler.warmup_steps}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Initialization error: {e}")
        return False


def test_transformer_configuration():
    """Test that the transformer model uses the updated configuration."""
    print("\n🧪 Testing Transformer Configuration...")
    
    try:
        from ml_core import MODEL_REGISTRY
        
        if 'transformer' in MODEL_REGISTRY:
            config = MODEL_REGISTRY['transformer']
            hyperparams = config['hyperparameters']
            
            # Check updated learning rate
            lr_default = hyperparams['learning_rate']['default']
            print(f"✅ Learning rate: {lr_default} (should be 1e-5)")
            
            # Check gradient clipping
            if 'gradient_clip_norm' in hyperparams:
                clip_default = hyperparams['gradient_clip_norm']['default']
                print(f"✅ Gradient clip norm: {clip_default} (should be 0.5)")
            else:
                print("⚠️ Gradient clip norm not found in config")
            
            # Check warmup steps
            if 'warmup_steps' in hyperparams:
                warmup_default = hyperparams['warmup_steps']['default']
                print(f"✅ Warmup steps: {warmup_default} (should be 1000)")
            else:
                print("⚠️ Warmup steps not found in config")
                
            return True
        else:
            print("❌ Transformer model not found in MODEL_REGISTRY")
            return False
            
    except Exception as e:
        print(f"❌ Configuration test error: {e}")
        return False


def test_phase1_preprocessing():
    """Test Phase 1 preprocessing with synthetic data similar to user's case."""
    print("\n🧪 Testing Phase 1 Preprocessing...")
    
    try:
        from utils.stability_core import phase1_preprocessing_pipeline
        
        # Create synthetic data similar to user's case
        n_sequences = 1000
        sequence_len = 64
        n_features = 5
        
        # Create data with realistic well log ranges
        sequences = np.random.randn(n_sequences, sequence_len, n_features)
        sequences[:, :, 0] = sequences[:, :, 0] * 50 + 100  # GR: ~50-150
        sequences[:, :, 1] = np.abs(sequences[:, :, 1]) * 0.3  # NPHI: 0-0.3
        sequences[:, :, 2] = sequences[:, :, 2] * 0.5 + 2.5  # RHOB: ~2-3
        sequences[:, :, 3] = sequences[:, :, 3] * 20 + 80   # DT: ~60-100
        sequences[:, :, 4] = sequences[:, :, 4] * 30 + 70   # Target log
        
        # Add missing values (similar to user's ~26% missing rate)
        missing_rate = 0.26
        missing_mask = np.random.random(sequences.shape) < missing_rate
        sequences[missing_mask] = np.nan
        
        print(f"   • Created synthetic data: {sequences.shape}")
        print(f"   • Missing values: {np.sum(np.isnan(sequences)):,} ({missing_rate:.1%})")
        
        # Apply Phase 1 preprocessing
        feature_names = ['GR', 'NPHI', 'RHOB', 'DT', 'TARGET']
        
        processed_sequences, metadata = phase1_preprocessing_pipeline(
            sequences=sequences,
            feature_names=feature_names,
            normalization_method='robust_standard',
            missing_encoding_method='learnable_embedding',
            validate_ranges=True,
            generate_report=False
        )
        
        print(f"   • Processed shape: {processed_sequences.shape}")
        print(f"   • Remaining NaN values: {np.sum(np.isnan(processed_sequences)):,}")
        
        # Check for stability
        finite_count = np.sum(np.isfinite(processed_sequences))
        total_count = np.prod(processed_sequences.shape)
        finite_rate = finite_count / total_count
        
        print(f"   • Finite values: {finite_rate:.1%}")
        
        if finite_rate > 0.9:
            print("✅ Phase 1 preprocessing successful - high finite rate")
            return True
        else:
            print(f"⚠️ Phase 1 preprocessing may have issues - finite rate: {finite_rate:.1%}")
            return False
            
    except Exception as e:
        print(f"❌ Phase 1 preprocessing test error: {e}")
        return False


def test_batch_validation():
    """Test batch validation function."""
    print("\n🧪 Testing Batch Validation...")
    
    try:
        from utils.stability_core import validate_batch_before_training
        
        # Create a good batch
        good_batch = torch.randn(32, 64, 5)
        good_batch[torch.rand_like(good_batch) < 0.1] = float('nan')  # 10% missing
        
        result = validate_batch_before_training(
            good_batch, 
            batch_idx=0, 
            feature_names=['GR', 'NPHI', 'RHOB', 'DT', 'TARGET']
        )
        print(f"✅ Good batch validation: {result}")
        
        # Create a problematic batch
        bad_batch = torch.full((32, 64, 5), float('nan'))  # All NaN
        bad_batch[:, :5, :] = torch.randn(32, 5, 5)  # Only 5 finite values
        
        result = validate_batch_before_training(
            bad_batch, 
            batch_idx=1, 
            feature_names=['GR', 'NPHI', 'RHOB', 'DT', 'TARGET']
        )
        print(f"✅ Bad batch validation: {result} (should be False)")
        
        return True
        
    except Exception as e:
        print(f"❌ Batch validation test error: {e}")
        return False


def main():
    """Run all stability tests."""
    print("🚀 Running Stability Fixes Test Suite")
    print("=" * 50)
    
    tests = [
        test_stability_components,
        test_transformer_configuration,
        test_phase1_preprocessing,
        test_batch_validation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    test_names = [
        "Stability Components",
        "Transformer Configuration", 
        "Phase 1 Preprocessing",
        "Batch Validation"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All stability fixes are working correctly!")
        print("\n💡 Recommendations for your training:")
        print("   1. Use learning_rate=1e-5 (already set in config)")
        print("   2. Use gradient_clip_norm=0.5 (already set in config)")
        print("   3. Enable Phase 1 preprocessing (use_phase1_preprocessing=True)")
        print("   4. Monitor the training logs for reduced gradient warnings")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    
    return passed == len(tests)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
