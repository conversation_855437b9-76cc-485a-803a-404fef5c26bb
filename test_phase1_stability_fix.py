"""
Test Phase 1 Stability Validation Fix

This script tests the fix for the stability validation issue where
Phase 1 preprocessing was rejecting training sequences with intentional
missing values.

The fix introduces training-aware validation that:
1. Allows intentional missing values in training sequences
2. Validates non-missing values for stability
3. Maintains strict validation for truth sequences
"""

import numpy as np
import sys
import os

def test_stability_validation_fix():
    """Test the fixed stability validation logic."""
    print("TESTING PHASE 1 STABILITY VALIDATION FIX")
    print("="*50)
    
    try:
        # Import the fixed functions
        from ml_core_phase1_integration import (
            validate_training_sequences,
            validate_training_sequences_with_missing
        )
        print("✅ Successfully imported fixed validation functions")
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    
    # Create test data similar to your scenario
    print("\n📊 Creating test data...")
    n_sequences = 100
    seq_len = 64
    n_features = 5
    
    # Create clean processed sequences (like after Phase 1)
    processed_sequences = np.random.randn(n_sequences, seq_len, n_features)
    
    # Normalize to realistic ranges
    processed_sequences[:, :, 0] = processed_sequences[:, :, 0] * 0.5 + 0.5  # GR normalized
    processed_sequences[:, :, 1] = np.abs(processed_sequences[:, :, 1]) * 0.3  # NPHI
    processed_sequences[:, :, 2] = processed_sequences[:, :, 2] * 0.3 + 0.7  # RHOB
    processed_sequences[:, :, 3] = processed_sequences[:, :, 3] * 0.4 + 0.6  # DT
    processed_sequences[:, :, 4] = processed_sequences[:, :, 4] * 0.5 + 0.5  # Target
    
    print(f"   Created processed sequences: {processed_sequences.shape}")
    print(f"   Missing values: {np.sum(np.isnan(processed_sequences))}")
    
    # Create training sequences with intentional missing values (like introduce_missingness)
    training_sequences = processed_sequences.copy()
    
    # Introduce 26.1% missing values (similar to your scenario)
    total_values = np.prod(training_sequences.shape)
    missing_rate = 0.261
    n_missing = int(total_values * missing_rate)
    
    # Randomly select positions to make missing
    flat_indices = np.random.choice(total_values, n_missing, replace=False)
    flat_training = training_sequences.flatten()
    flat_training[flat_indices] = np.nan
    training_sequences = flat_training.reshape(training_sequences.shape)
    
    actual_missing = np.sum(np.isnan(training_sequences))
    actual_missing_rate = actual_missing / total_values
    
    print(f"   Created training sequences with missing values:")
    print(f"     • Target missing rate: {missing_rate:.1%}")
    print(f"     • Actual missing values: {actual_missing:,}")
    print(f"     • Actual missing rate: {actual_missing_rate:.1%}")
    
    feature_names = ['GR', 'NPHI', 'RHOB', 'DT', 'TARGET']
    
    # Test 1: Validate truth sequences (should pass - no missing values)
    print("\n🔧 Test 1: Truth Sequences Validation")
    truth_stable = validate_training_sequences(
        processed_sequences, 
        feature_names, 
        allow_missing=False,
        max_missing_rate=0.0
    )
    print(f"   Truth sequences stable: {'✅ PASS' if truth_stable else '❌ FAIL'}")
    
    # Test 2: Validate training sequences with old method (should fail)
    print("\n🔧 Test 2: Training Sequences - Old Validation (Expected to Fail)")
    try:
        from utils.stability_core import enhanced_validate_sequences
        old_validation = enhanced_validate_sequences(training_sequences, feature_names)
        print(f"   Old validation result: {'✅ PASS' if old_validation else '❌ FAIL (Expected)'}")
    except ImportError:
        print("   Old validation not available - skipping")
        old_validation = False
    
    # Test 3: Validate training sequences with new method (should pass)
    print("\n🔧 Test 3: Training Sequences - New Validation (Should Pass)")
    new_validation = validate_training_sequences(
        training_sequences, 
        feature_names, 
        allow_missing=True,
        max_missing_rate=0.5
    )
    print(f"   New validation result: {'✅ PASS' if new_validation else '❌ FAIL'}")
    
    # Test 4: Test edge cases
    print("\n🔧 Test 4: Edge Cases")
    
    # Test 4a: Too high missing rate
    very_missing_sequences = processed_sequences.copy()
    very_missing_flat = very_missing_sequences.flatten()
    very_missing_indices = np.random.choice(len(very_missing_flat), int(len(very_missing_flat) * 0.8), replace=False)
    very_missing_flat[very_missing_indices] = np.nan
    very_missing_sequences = very_missing_flat.reshape(very_missing_sequences.shape)
    
    high_missing_validation = validate_training_sequences_with_missing(
        very_missing_sequences, feature_names, max_missing_rate=0.5
    )
    print(f"   High missing rate (80%): {'✅ PASS' if high_missing_validation else '❌ FAIL (Expected)'}")
    
    # Test 4b: Infinite values
    inf_sequences = training_sequences.copy()
    inf_sequences[0, 0, 0] = np.inf
    
    inf_validation = validate_training_sequences_with_missing(
        inf_sequences, feature_names, max_missing_rate=0.5
    )
    print(f"   With infinite values: {'✅ PASS' if inf_validation else '❌ FAIL (Expected)'}")
    
    # Test 4c: Extreme values
    extreme_sequences = training_sequences.copy()
    extreme_sequences[0, 0, 0] = 1e8
    
    extreme_validation = validate_training_sequences_with_missing(
        extreme_sequences, feature_names, max_missing_rate=0.5
    )
    print(f"   With extreme values: {'✅ PASS' if extreme_validation else '❌ FAIL (Expected)'}")
    
    # Summary
    print("\n📋 SUMMARY")
    print("="*30)
    
    expected_results = {
        'Truth sequences (no missing)': truth_stable,
        'Training sequences (old method)': not old_validation,  # Should fail
        'Training sequences (new method)': new_validation,  # Should pass
        'High missing rate rejection': not high_missing_validation,  # Should fail
        'Infinite values rejection': not inf_validation,  # Should fail
        'Extreme values rejection': not extreme_validation  # Should fail
    }
    
    all_passed = all(expected_results.values())
    
    for test_name, passed in expected_results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🚀 STABILITY VALIDATION FIX VERIFIED!")
        print("   • Truth sequences: Strict validation (no missing values)")
        print("   • Training sequences: Flexible validation (allows intentional missing)")
        print("   • Edge cases: Properly rejected")
        print("   • Your Phase 1 integration should now work correctly!")
    
    return all_passed


def demonstrate_fix_explanation():
    """Explain what was fixed and why."""
    print("\n" + "="*60)
    print("EXPLANATION OF THE FIX")
    print("="*60)
    
    print("\n🔴 THE PROBLEM:")
    print("   1. Phase 1 preprocessing eliminates all missing values → ✅ Good")
    print("   2. Training requires artificial missing values → ✅ Necessary")
    print("   3. introduce_missingness() adds 26.1% NaN values → ✅ Correct")
    print("   4. enhanced_validate_sequences() rejects ANY NaN → ❌ Too strict")
    print("   5. Result: 'Training sequences: UNSTABLE' → ❌ False alarm")
    
    print("\n🟢 THE SOLUTION:")
    print("   1. Created validate_training_sequences() → ✅ Training-aware")
    print("   2. allow_missing=True for training sequences → ✅ Flexible")
    print("   3. allow_missing=False for truth sequences → ✅ Strict")
    print("   4. Validates non-missing values for stability → ✅ Smart")
    print("   5. Result: Proper validation for both cases → ✅ Fixed")
    
    print("\n🔧 TECHNICAL DETAILS:")
    print("   • Truth sequences: Must have 0% missing (after Phase 1)")
    print("   • Training sequences: Can have up to 50% missing (intentional)")
    print("   • Non-missing values: Checked for inf, extreme values, variance")
    print("   • Missing values: Allowed in training, forbidden in truth")
    
    print("\n📊 EXPECTED BEHAVIOR NOW:")
    print("   ✅ Phase 1 preprocessing: Eliminates real missing values")
    print("   ✅ Missing value introduction: Adds training missing values")
    print("   ✅ Truth validation: STABLE (no missing values)")
    print("   ✅ Training validation: STABLE (intentional missing values)")
    print("   ✅ Model training: Proceeds without stability warnings")


def main():
    """Main test function."""
    print("PHASE 1 STABILITY VALIDATION FIX TEST")
    print("="*60)
    
    # Run the fix test
    success = test_stability_validation_fix()
    
    # Explain the fix
    demonstrate_fix_explanation()
    
    print("\n" + "="*60)
    print("NEXT STEPS")
    print("="*60)
    
    if success:
        print("✅ The stability validation fix is working correctly!")
        print("\n🚀 You can now run your main.py script:")
        print("   1. The Phase 1 preprocessing will clean your data")
        print("   2. Training sequences will be validated with the new logic")
        print("   3. You should see 'Training sequences: ✅ STABLE'")
        print("   4. No more false stability warnings!")
        print("   5. Training will proceed smoothly")
    else:
        print("❌ Some tests failed - please check the implementation")
        print("   The fix may need additional adjustments")
    
    return success


if __name__ == "__main__":
    main()
